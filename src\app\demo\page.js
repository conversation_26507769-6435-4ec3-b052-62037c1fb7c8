import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Play, Calendar, Clock, Users, CheckCircle } from "lucide-react";

export default function DemoPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              See <span className="text-primary">Auris GRCOS</span> in Action
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Get a preview of our revolutionary compliance management platform. See how Auris GRCOS
              will transform your organization's approach to governance, risk, and compliance management.
            </p>
          </div>
        </div>
      </section>

      {/* Demo Options */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Watch Demo */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Play className="h-8 w-8 text-primary-foreground" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Watch Product Demo</h2>
              <p className="text-muted-foreground mb-6">
                Get an overview of the Auris GRCOS platform with our comprehensive product demonstration video.
                See key features, workflows, and capabilities in action.
              </p>
              <ul className="space-y-2 mb-8">
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Platform overview and architecture
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Key modules and integrations
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Compliance workflow examples
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Reporting and analytics features
                </li>
              </ul>
              <Button size="lg" className="w-full" asChild>
                <Link href="/demo/watch">
                  <Play className="mr-2 h-4 w-4" />
                  Watch Demo Video
                </Link>
              </Button>
            </div>

            {/* Book Demo */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Calendar className="h-8 w-8 text-primary-foreground" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Join Our Waitlist</h2>
              <p className="text-muted-foreground mb-6">
                Be among the first to access Auris GRCOS when we launch. Join our waitlist for early access,
                exclusive updates, and special pricing for early supporters.
              </p>
              <ul className="space-y-2 mb-8">
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Early access when we launch
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Exclusive updates and insights
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Special pricing for early supporters
                </li>
                <li className="flex items-center text-sm">
                  <CheckCircle className="h-4 w-4 text-primary mr-2" />
                  Direct access to founding team
                </li>
              </ul>
              <Button size="lg" className="w-full" asChild>
                <Link href="/waitlist">
                  <Calendar className="mr-2 h-4 w-4" />
                  Join Waitlist
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              What You'll See in the Demo
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Our comprehensive demonstration covers all aspects of the Auris GRCOS platform.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Users className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">User Management</h3>
              <p className="text-muted-foreground">
                Role-based access controls, user provisioning, and compliance team collaboration features.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <CheckCircle className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Compliance Tracking</h3>
              <p className="text-muted-foreground">
                Real-time compliance status monitoring, automated assessments, and gap analysis tools.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Clock className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Automated Workflows</h3>
              <p className="text-muted-foreground">
                Streamlined compliance processes, automated notifications, and task management systems.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Stats */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-3 text-center">
              <div>
                <div className="text-3xl font-bold text-primary mb-2">30 min</div>
                <div className="text-muted-foreground">Average demo duration</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">500+</div>
                <div className="text-muted-foreground">Companies have seen our demo</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">95%</div>
                <div className="text-muted-foreground">Would recommend to others</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Ready to Transform Your Compliance?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Join hundreds of organizations that have streamlined their compliance processes with Auris GRCOS.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
