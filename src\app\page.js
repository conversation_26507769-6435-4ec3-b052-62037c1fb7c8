import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, Zap, Users, CheckCircle } from "lucide-react";

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Streamline Your{" "}
              <span className="text-primary">Compliance</span>{" "}
              Management
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Intelligent compliance management platform for modern businesses.
              Automate frameworks like ISO 27001, HIPAA, SOC 2, and more with our comprehensive GRCOS architecture.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo/request">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo/watch">Watch Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Why Choose Auris Compliance?
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Built for modern businesses that need comprehensive compliance management without the complexity.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Comprehensive Frameworks</h3>
                <p className="mt-2 text-muted-foreground">
                  Support for ISO 27001, HIPAA, SOC 2, PCI DSS, POPIA, and more compliance frameworks.
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Intelligent Automation</h3>
                <p className="mt-2 text-muted-foreground">
                  Automate compliance tasks, monitoring, and reporting with our GRCOS platform.
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <Users className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Expert Support</h3>
                <p className="mt-2 text-muted-foreground">
                  Access to certified auditors and compliance experts through our directory.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions by Industry */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Solutions for Every Industry
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Tailored compliance solutions designed for your specific industry needs.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[
                { name: "Financial Services", href: "/solutions/industry/financial-services" },
                { name: "Healthcare", href: "/solutions/industry/healthcare" },
                { name: "Technology", href: "/solutions/industry/technology" },
                { name: "Ecommerce", href: "/solutions/industry/ecommerce" },
                { name: "Education", href: "/solutions/industry/education" },
                { name: "Manufacturing", href: "/solutions/industry/manufacturing" }
              ].map((industry) => (
                <Link
                  key={industry.name}
                  href={industry.href}
                  className="group rounded-lg border p-6 hover:bg-muted/50 transition-colors"
                >
                  <h3 className="font-semibold group-hover:text-primary transition-colors">
                    {industry.name}
                  </h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Specialized compliance solutions for {industry.name.toLowerCase()} organizations.
                  </p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Ready to Streamline Your Compliance?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Join hundreds of companies that trust Auris Compliance to manage their regulatory requirements.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo/request">
                  Get Started Today
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
