import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Database, Plug, Cloud } from "lucide-react";

export default function IntegrationsPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Platform <span className="text-primary">Integrations</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Connect Auris GRCOS with your existing tools and systems through our comprehensive 
              API and pre-built integrations ecosystem.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Types */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Integration Capabilities
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Seamlessly connect with your existing technology stack.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Database className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Data Sources</h3>
              <p className="text-muted-foreground">
                Connect to databases, data warehouses, and business intelligence tools.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Plug className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Third-Party Tools</h3>
              <p className="text-muted-foreground">
                Integrate with security tools, HR systems, and business applications.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Cloud className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Cloud Platforms</h3>
              <p className="text-muted-foreground">
                Native integrations with AWS, Azure, Google Cloud, and other platforms.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Integration Marketplace Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              We're building a comprehensive integration marketplace with pre-built connectors 
              for popular business tools and platforms.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
