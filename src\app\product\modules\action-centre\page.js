import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { <PERSON>R<PERSON>, Setting<PERSON>, Clock, Users } from "lucide-react";

export default function ActionCentrePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">Action Centre</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Task and workflow management system to streamline compliance processes and ensure 
              timely completion of all regulatory requirements.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Workflow Management
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Streamline your compliance workflows with intelligent task management.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Settings className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Task Automation</h3>
              <p className="text-muted-foreground">
                Automated task creation and assignment based on compliance requirements and deadlines.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Clock className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Deadline Tracking</h3>
              <p className="text-muted-foreground">
                Real-time deadline tracking with automated reminders and escalation workflows.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Users className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Team Collaboration</h3>
              <p className="text-muted-foreground">
                Collaborative workspace for teams to work together on compliance tasks and projects.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Module Details Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Complete feature documentation for the Action Centre module will be available 
              when we launch the platform.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
