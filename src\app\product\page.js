import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, Zap, Users, Database, Settings, BarChart3 } from "lucide-react";

export default function ProductPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              The <span className="text-primary">GRCOS Platform</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Our comprehensive compliance management platform built on the GRCOS (Governance, Risk, and Compliance Operating System) architecture. 
              Streamline your compliance processes with intelligent automation and seamless integrations.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo/request">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/product/grcos-architecture">Learn More</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Overview */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Platform Components
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Discover the core components that make up our comprehensive compliance platform.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <Link href="/product/grcos-architecture" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  GRCOS Architecture
                </h3>
                <p className="text-muted-foreground mb-4">
                  Core platform architecture designed for scalable compliance management and governance.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/product/integrations" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Database className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Integrations
                </h3>
                <p className="text-muted-foreground mb-4">
                  Connect with your existing tools and systems through our comprehensive API and integration hub.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/product/platform-compliance" className="group">
              <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <BarChart3 className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  Platform Compliance
                </h3>
                <p className="text-muted-foreground mb-4">
                  Built-in compliance features and controls to ensure your platform meets regulatory requirements.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Modules Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Platform Modules
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Powerful modules designed to handle every aspect of your compliance management needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <Link href="/product/modules/integration-hub" className="group">
              <div className="rounded-lg border bg-background p-8 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Database className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                  Integration Hub
                </h3>
                <p className="text-muted-foreground mb-4">
                  Centralized data integration management for seamless connectivity with your existing systems and third-party tools.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Explore module <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/product/modules/compliance-management" className="group">
              <div className="rounded-lg border bg-background p-8 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                  Compliance Management
                </h3>
                <p className="text-muted-foreground mb-4">
                  Comprehensive compliance tracking and reporting tools to manage multiple frameworks and regulatory requirements.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Explore module <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/product/modules/action-centre" className="group">
              <div className="rounded-lg border bg-background p-8 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Settings className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                  Action Centre
                </h3>
                <p className="text-muted-foreground mb-4">
                  Task and workflow management system to streamline compliance processes and ensure timely completion of requirements.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Explore module <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>

            <Link href="/product/modules/trust-centre" className="group">
              <div className="rounded-lg border bg-background p-8 hover:shadow-lg transition-all">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Users className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                  Trust Centre
                </h3>
                <p className="text-muted-foreground mb-4">
                  Security and trust transparency portal to showcase your compliance status and build customer confidence.
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Explore module <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Ready to Experience GRCOS?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              See how our platform can transform your compliance management processes.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo/request">
                  Schedule Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
