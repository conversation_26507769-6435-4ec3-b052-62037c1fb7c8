import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { ArrowRight, Shield, Building, Users, Briefcase } from "lucide-react";

export default function SolutionsPage() {
  const frameworks = [
    { name: "ISO 27001", href: "/solutions/framework/iso-27001", description: "Information security management compliance" },
    { name: "HIPAA", href: "/solutions/framework/hipaa", description: "Healthcare data protection compliance" },
    { name: "SOC 2", href: "/solutions/framework/soc-2", description: "Service organization control compliance" },
    { name: "PCI DSS", href: "/solutions/framework/pci-dss", description: "Payment card industry data security" },
    { name: "POPIA", href: "/solutions/framework/popia", description: "South African data protection compliance" },
    { name: "GDPR", href: "/solutions/framework/gdpr", description: "European data protection regulation" }
  ];

  const industries = [
    { name: "Financial Services", href: "/solutions/industry/financial-services", description: "Banking and fintech compliance solutions" },
    { name: "Healthcare", href: "/solutions/industry/healthcare", description: "Medical and health tech compliance" },
    { name: "Technology", href: "/solutions/industry/technology", description: "Tech company compliance frameworks" },
    { name: "Ecommerce", href: "/solutions/industry/ecommerce", description: "Online retail compliance management" },
    { name: "Education", href: "/solutions/industry/education", description: "Educational institution compliance" },
    { name: "Manufacturing", href: "/solutions/industry/manufacturing", description: "Industrial compliance requirements" }
  ];

  const sizes = [
    { name: "Startups", href: "/solutions/size/startups", description: "Early-stage company compliance solutions", icon: Users },
    { name: "SMBs", href: "/solutions/size/smbs", description: "Small to medium business packages", icon: Building },
    { name: "Enterprise", href: "/solutions/size/enterprise", description: "Large organization compliance management", icon: Briefcase }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Compliance Solutions for <span className="text-primary">Every Business</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Whether you're managing ISO 27001, HIPAA, SOC 2, or any other compliance framework, 
              we have tailored solutions for your industry, size, and specific requirements.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo/request">
                  Find Your Solution
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Speak with Expert</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* By Framework */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Solutions by Framework
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Comprehensive support for major compliance frameworks and regulations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {frameworks.map((framework) => (
              <Link
                key={framework.name}
                href={framework.href}
                className="group rounded-lg border bg-background p-6 hover:shadow-lg transition-all"
              >
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  {framework.name}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {framework.description}
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* By Industry */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Solutions by Industry
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Industry-specific compliance solutions tailored to your sector's unique requirements.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {industries.map((industry) => (
              <Link
                key={industry.name}
                href={industry.href}
                className="group rounded-lg border p-6 hover:bg-muted/50 transition-colors"
              >
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  {industry.name}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {industry.description}
                </p>
                <div className="flex items-center text-primary text-sm font-medium">
                  Explore solutions <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* By Company Size */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Solutions by Company Size
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Scalable compliance solutions designed for organizations of every size.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {sizes.map((size) => {
              const Icon = size.icon;
              return (
                <Link
                  key={size.name}
                  href={size.href}
                  className="group rounded-lg border bg-background p-8 hover:shadow-lg transition-all text-center"
                >
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                    <Icon className="h-8 w-8 text-primary-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {size.name}
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    {size.description}
                  </p>
                  <div className="flex items-center justify-center text-primary text-sm font-medium">
                    View solutions <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Not Sure Which Solution is Right for You?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Our compliance experts can help you identify the best approach for your specific needs and requirements.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Speak with Expert
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/demo/request">Book Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
