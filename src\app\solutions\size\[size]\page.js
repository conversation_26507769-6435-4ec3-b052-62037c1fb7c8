import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Users, Building, Briefcase, Zap } from "lucide-react";
import { notFound } from "next/navigation";

// Company size data
const sizeData = {
  "startups": {
    name: "Startups",
    title: "Startup Compliance Solutions",
    description: "Early-stage company compliance solutions designed for rapid growth.",
    longDescription: "Compliance solutions designed for startups and early-stage companies. Get compliance-ready quickly without the complexity of enterprise solutions.",
    icon: Users,
    features: [
      "Quick setup and implementation",
      "Essential compliance frameworks",
      "Affordable pricing for early-stage companies",
      "Scalable as you grow"
    ],
    benefits: [
      "Investor-ready compliance posture",
      "Customer trust from day one",
      "Reduced time to market",
      "Foundation for future growth"
    ]
  },
  "smbs": {
    name: "SMBs",
    title: "Small to Medium Business Solutions",
    description: "Comprehensive compliance packages for growing businesses.",
    longDescription: "Tailored compliance solutions for small to medium businesses that need comprehensive coverage without enterprise complexity.",
    icon: Building,
    features: [
      "Multi-framework support",
      "Automated compliance workflows",
      "Team collaboration tools",
      "Regular compliance assessments"
    ],
    benefits: [
      "Competitive advantage through compliance",
      "Reduced compliance overhead",
      "Better risk management",
      "Preparation for enterprise growth"
    ]
  },
  "enterprise": {
    name: "Enterprise",
    title: "Enterprise Compliance Management",
    description: "Large organization compliance management with advanced features.",
    longDescription: "Comprehensive compliance management for large organizations with complex requirements, multiple frameworks, and global operations.",
    icon: Briefcase,
    features: [
      "Advanced workflow automation",
      "Multi-tenant architecture",
      "Global compliance support",
      "Enterprise integrations"
    ],
    benefits: [
      "Centralized compliance management",
      "Reduced compliance costs",
      "Enhanced audit readiness",
      "Global regulatory coverage"
    ]
  }
};

export default function SizePage({ params }) {
  const size = sizeData[params.size];
  
  if (!size) {
    notFound();
  }

  const Icon = size.icon;

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-lg bg-primary mb-8">
              <Icon className="h-10 w-10 text-primary-foreground" />
            </div>
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">{size.name}</span> Compliance Solutions
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              {size.longDescription}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Features for {size.name}
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Compliance features tailored to the needs of {size.name.toLowerCase()}.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {size.features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4 p-6 rounded-lg border bg-background">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  {index + 1}
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{feature}</h3>
                  <p className="text-muted-foreground">
                    Designed specifically to meet the compliance needs of {size.name.toLowerCase()}.
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Benefits for {size.name}
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Why {size.name.toLowerCase()} choose Auris GRCOS for their compliance needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {size.benefits.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{benefit}</h3>
                  <p className="text-muted-foreground">
                    Key advantage that {size.name.toLowerCase()} gain from using our compliance platform.
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Hint */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Pricing for {size.name}
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              {size.name === "Startups" && "Affordable pricing designed for early-stage companies with limited budgets."}
              {size.name === "SMBs" && "Competitive pricing that scales with your business growth and compliance needs."}
              {size.name === "Enterprise" && "Enterprise pricing with volume discounts and custom packages available."}
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist for Early Pricing
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Detailed Solutions Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Comprehensive documentation and pricing for {size.name.toLowerCase()} solutions 
              will be available when we launch.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(sizeData).map((size) => ({
    size: size,
  }));
}
