import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { Separator } from "@/components/ui/separator";
import { footerData } from "@/lib/utils";

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-5">
          {/* Logo and Tagline */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded bg-primary"></div>
              <span className="text-xl font-bold">Auris Compliance</span>
            </Link>
            <p className="text-sm text-muted-foreground mb-4">
              Streamlining compliance management for modern businesses with intelligent automation and comprehensive frameworks.
            </p>
          </div>

          {/* Footer Links */}
          {footerData.sections.map((section) => (
            <div key={section.title} className="space-y-3">
              <h4 className="text-sm font-semibold">{section.title}</h4>
              <ul className="space-y-2">
                {section.items.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                      {...(item.external && { target: "_blank", rel: "noopener noreferrer" })}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* Login Button */}
          <div className="lg:col-span-1 flex flex-col items-start lg:items-end">
            <Button variant="outline" asChild>
              <Link href="/login">Log In</Link>
            </Button>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Bottom Section */}
        <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
          <div className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} Auris Compliance. All rights reserved.
          </div>
          <div className="text-sm text-muted-foreground">
            <Link href="mailto:<EMAIL>" className="hover:text-foreground transition-colors">
              <EMAIL>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
