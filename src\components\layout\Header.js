"use client";

import Link from "next/link";
import { useState } from "react";
import {
  Navbar,
  NavBody,
  MobileNav,
  MobileNavHeader,
  MobileNavMenu,
  MobileNavToggle,
  NavbarButton
} from "@/components/ui/resizable-navbar";
import { MegaMenuNavItems } from "@/components/layout/MegaMenu";
import { navigationData } from "@/lib/utils";

export function Header() {
  const [isOpen, setIsOpen] = useState(false);

  const mobileNavItems = [
    { name: "Product", link: "/product" },
    { name: "Solutions", link: "/solutions" },
    { name: "Company", link: "/company" },
    { name: "Resources", link: "/resources" },
    { name: "Contact", link: "/contact" }
  ];

  return (
    <Navbar className="fixed top-0 left-0 right-0">
      {/* Desktop Navigation */}
      <NavBody>
        {/* Logo */}
        <Link href="/" className="relative z-20 mr-4 flex items-center space-x-2 px-2 py-1 text-sm font-normal text-black">
          <div className="h-8 w-8 rounded bg-primary"></div>
          <span className="font-medium text-black dark:text-white">Auris Compliance</span>
        </Link>

        {/* Navigation Items with Mega Menu */}
        <MegaMenuNavItems />

        {/* CTA Buttons */}
        <div className="flex items-center space-x-4">
          <NavbarButton
            href="/demo"
            variant="dark"
            as={Link}
          >
            See Demo
          </NavbarButton>
          <NavbarButton
            href="/waitlist"
            variant="primary"
            as={Link}
          >
            Join Waitlist
          </NavbarButton>
        </div>
      </NavBody>

      {/* Mobile Navigation */}
      <MobileNav>
        <MobileNavHeader>
          {/* Mobile Logo */}
          <Link href="/" className="flex items-center space-x-2 px-2 py-1">
            <div className="h-6 w-6 rounded bg-primary"></div>
            <span className="font-medium text-black dark:text-white">Auris Compliance</span>
          </Link>

          {/* Mobile Menu Toggle */}
          <MobileNavToggle
            isOpen={isOpen}
            onClick={() => setIsOpen(!isOpen)}
          />
        </MobileNavHeader>

        {/* Mobile Menu */}
        <MobileNavMenu isOpen={isOpen} onClose={() => setIsOpen(false)}>
          <div className="flex flex-col space-y-4 w-full">
            {mobileNavItems.map((item, idx) => (
              <Link
                key={`mobile-${idx}`}
                href={item.link}
                className="text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                {item.name}
              </Link>
            ))}

            <div className="pt-4 border-t space-y-2">
              <NavbarButton
                href="/demo"
                variant="dark"
                as={Link}
                className="w-full"
              >
                See Demo
              </NavbarButton>
              <NavbarButton
                href="/waitlist"
                variant="primary"
                as={Link}
                className="w-full"
              >
                Join Waitlist
              </NavbarButton>
            </div>
          </div>
        </MobileNavMenu>
      </MobileNav>
    </Navbar>
  );
}
